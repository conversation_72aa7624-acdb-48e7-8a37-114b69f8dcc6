{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeNinjaFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray<PERSON>lang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompiler.cmake.in"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3Config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3headersTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3testTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3testTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttfConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttfConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttf-shared-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttf-shared-targets-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build", "source": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice"}, "version": {"major": 1, "minor": 1}}